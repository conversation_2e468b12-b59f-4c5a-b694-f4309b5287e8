import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import dotenv from 'dotenv';
import path from 'path';
import { createServer } from 'http';

// 加载环境变量
dotenv.config();

// 导入路由
import authRouter from './routes/auth';
import studentRouter from './routes/student';
import recommendationRouter from './routes/recommendation';
import careerRouter from './routes/career';
import simulationRouter from './routes/simulation';
import paymentRouter from './routes/payment';
import statisticsRouter from './routes/statistics';
import configRouter from './routes/config';
import adminRouter from './routes/admin';

// 导入中间件
import { errorHandler } from './middleware/errorHandler';
import { rateLimiter } from './middleware/rateLimiter';
import { logger } from './utils/logger';

// 导入数据库连接
import { connectDatabase } from './config/database';
import { connectRedis } from './config/redis';

const app = express();
const PORT = process.env.PORT || 3000;

// 基础中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: [
        "'self'",
        "'unsafe-inline'",
        "'unsafe-eval'",
        "https://cdn.jsdelivr.net",
        "https://cdnjs.cloudflare.com"
      ],
      styleSrc: [
        "'self'",
        "'unsafe-inline'",
        "https://cdn.jsdelivr.net",
        "https://cdnjs.cloudflare.com"
      ],
      fontSrc: [
        "'self'",
        "https://cdn.jsdelivr.net",
        "https://cdnjs.cloudflare.com"
      ],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  }
}));
app.use(compression());
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://your-domain.com'] 
    : true,
  credentials: true
}));

// 请求解析
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));
app.use('/admin', express.static(path.join(__dirname, '../public/admin')));

// 请求日志
app.use((req, _res, next) => {
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// 限流中间件
app.use(rateLimiter);

// API路由
app.use('/v1/auth', authRouter);
app.use('/v1/student', studentRouter);
app.use('/v1/recommendation', recommendationRouter);
app.use('/v1/career', careerRouter);
app.use('/v1/simulation', simulationRouter);
app.use('/v1/payment', paymentRouter);
app.use('/v1/statistics', statisticsRouter);
app.use('/v1/config', configRouter);
app.use('/v1/admin', adminRouter);

// 健康检查
app.get('/health', (_req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// 管理后台首页
app.get('/admin', (_req, res) => {
  res.sendFile(path.join(__dirname, '../public/admin/index.html'));
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    code: 404,
    message: 'API接口不存在',
    path: req.originalUrl
  });
});

// 错误处理中间件
app.use(errorHandler);

// 创建服务器
const server = createServer(app);

// 启动服务器
async function startServer() {
  try {
    // 连接数据库
    await connectDatabase();
    logger.info('数据库连接成功');

    // 连接Redis
    await connectRedis();
    logger.info('Redis连接成功');

    // 启动服务器
    server.listen(PORT, () => {
      logger.info(`服务器启动成功，端口: ${PORT}`);
      logger.info(`管理后台: http://localhost:${PORT}/admin`);
      logger.info(`API文档: http://localhost:${PORT}/v1`);
    });
  } catch (error) {
    logger.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('收到SIGTERM信号，开始优雅关闭...');
  server.close(() => {
    logger.info('服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('收到SIGINT信号，开始优雅关闭...');
  server.close(() => {
    logger.info('服务器已关闭');
    process.exit(0);
  });
});

startServer();

export default app; 