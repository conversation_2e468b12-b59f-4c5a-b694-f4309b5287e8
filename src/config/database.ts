import mysql from 'mysql2/promise';
import bcrypt from 'bcryptjs';
import { logger } from '../utils/logger';

let connection: mysql.Connection;

export const connectDatabase = async () => {
  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'zhiyuan_ai'
    });

    // 测试连接
    await connection.ping();
    logger.info('MySQL数据库连接成功');

    // 创建数据库表
    await createTables();

    // 创建默认管理员
    await createDefaultAdmin();

    // 创建测试数据
    await createTestData();

    return connection;
  } catch (error) {
    logger.error('MySQL数据库连接失败:', error);
    throw error;
  }
};

export const getConnection = () => {
  if (!connection) {
    throw new Error('数据库未连接');
  }
  return connection;
};

// 创建默认管理员账户
const createDefaultAdmin = async () => {
  try {
    const [existingAdmins] = await connection.execute(
      'SELECT id FROM admins WHERE username = ?',
      ['admin']
    );

    if ((existingAdmins as any[]).length === 0) {
      const hashedPassword = await bcrypt.hash('admin123456', 10);
      await connection.execute(
        'INSERT INTO admins (username, password, email, role) VALUES (?, ?, ?, ?)',
        ['admin', hashedPassword, '<EMAIL>', 'super']
      );
      logger.info('默认管理员账户创建成功: admin/admin123456');
    }
  } catch (error) {
    logger.error('创建默认管理员失败:', error);
  }
};

// 创建测试数据
const createTestData = async () => {
  try {
    // 检查是否已有测试数据
    const [existingUsers] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const userCount = (existingUsers as any[])[0].count;

    if (userCount === 0) {
      // 创建测试用户
      const testUsers = [
        { id: 'user_001', phone: '13800138001', nickname: '张三', status: 'active' },
        { id: 'user_002', phone: '13800138002', nickname: '李四', status: 'active' },
        { id: 'user_003', phone: '13800138003', nickname: '王五', status: 'inactive' }
      ];

      for (const user of testUsers) {
        await connection.execute(
          'INSERT INTO users (id, phone, nickname, status, register_time) VALUES (?, ?, ?, ?, NOW())',
          [user.id, user.phone, user.nickname, user.status]
        );
      }

      // 创建测试考生档案
      const testProfiles = [
        {
          id: 'profile_001',
          user_id: 'user_001',
          name: '张三',
          score: 612,
          province: 'hubei',
          subject_combination: 'physics_chemistry_biology',
          exam_type: 'new_gaokao',
          family_economic_status: 'middle_class'
        },
        {
          id: 'profile_002',
          user_id: 'user_002',
          name: '李四',
          score: 578,
          province: 'beijing',
          subject_combination: 'history_politics_geography',
          exam_type: 'new_gaokao',
          family_economic_status: 'high_income'
        }
      ];

      for (const profile of testProfiles) {
        await connection.execute(
          `INSERT INTO student_profiles (
            id, user_id, name, score, province, subject_combination, exam_type, family_economic_status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            profile.id, profile.user_id, profile.name, profile.score,
            profile.province, profile.subject_combination, profile.exam_type, profile.family_economic_status
          ]
        );
      }

      // 创建测试院校专业数据
      const testUniversities = [
        {
          id: 'uni_001',
          university_id: 'hust_001',
          university_name: '华中科技大学',
          major_id: 'cs_001',
          major_name: '计算机科学与技术',
          university_tier: '985',
          province: 'hubei',
          city: '武汉'
        },
        {
          id: 'uni_002',
          university_id: 'pku_001',
          university_name: '北京大学',
          major_id: 'econ_001',
          major_name: '经济学',
          university_tier: '985',
          province: 'beijing',
          city: '北京'
        },
        {
          id: 'uni_003',
          university_id: 'whu_001',
          university_name: '武汉大学',
          major_id: 'law_001',
          major_name: '法学',
          university_tier: '985',
          province: 'hubei',
          city: '武汉'
        }
      ];

      for (const uni of testUniversities) {
        await connection.execute(
          `INSERT INTO universities_majors (
            id, university_id, university_name, major_id, major_name, university_tier, province, city
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            uni.id, uni.university_id, uni.university_name, uni.major_id,
            uni.major_name, uni.university_tier, uni.province, uni.city
          ]
        );
      }

      // 创建测试职业数据
      const testCareers = [
        {
          id: 'career_001',
          career_name: '软件开发工程师',
          career_category: 'technology',
          description: '负责软件系统的设计、开发、测试和维护',
          salary_data: JSON.stringify({ average: 15000 }),
          employment_prospects: JSON.stringify({ prospect: 'excellent' })
        },
        {
          id: 'career_002',
          career_name: '金融分析师',
          career_category: 'finance',
          description: '分析金融市场趋势，为投资决策提供建议',
          salary_data: JSON.stringify({ average: 12000 }),
          employment_prospects: JSON.stringify({ prospect: 'good' })
        }
      ];

      for (const career of testCareers) {
        await connection.execute(
          `INSERT INTO careers (
            id, career_name, career_category, description, salary_data, employment_prospects
          ) VALUES (?, ?, ?, ?, ?, ?)`,
          [
            career.id, career.career_name, career.career_category,
            career.description, career.salary_data, career.employment_prospects
          ]
        );
      }

      logger.info('测试数据创建成功');
    }
  } catch (error) {
    logger.error('创建测试数据失败:', error);
  }
};

// 创建数据库表
const createTables = async () => {
  try {
    // 用户表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(50) PRIMARY KEY,
        phone VARCHAR(20) UNIQUE NOT NULL,
        nickname VARCHAR(50),
        avatar_url VARCHAR(200),
        register_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login_time TIMESTAMP,
        status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
        invite_code VARCHAR(20),
        invited_by VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 管理员表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS admins (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100),
        role ENUM('super', 'admin', 'operator') DEFAULT 'admin',
        last_login_time TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 考生档案表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS student_profiles (
        id VARCHAR(50) PRIMARY KEY,
        user_id VARCHAR(50) NOT NULL,
        name VARCHAR(50) NOT NULL,
        score INT NOT NULL,
        province VARCHAR(20) NOT NULL,
        subject_combination VARCHAR(50) NOT NULL,
        exam_type ENUM('new_gaokao', 'traditional') NOT NULL,
        preferred_regions JSON,
        major_preferences JSON,
        family_economic_status VARCHAR(20),
        special_requirements JSON,
        predicted_rank JSON,
        school_tier_prediction VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 院校专业表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS universities_majors (
        id VARCHAR(50) PRIMARY KEY,
        university_id VARCHAR(50) NOT NULL,
        university_name VARCHAR(100) NOT NULL,
        major_id VARCHAR(50) NOT NULL,
        major_name VARCHAR(100) NOT NULL,
        university_tier ENUM('985', '211', 'tier1', 'tier2') NOT NULL,
        province VARCHAR(20) NOT NULL,
        city VARCHAR(50) NOT NULL,
        subject_requirements JSON,
        physical_requirements JSON,
        historical_scores JSON,
        employment_data JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_university_tier (university_tier),
        INDEX idx_province (province)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 推荐记录表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS recommendations (
        id VARCHAR(50) PRIMARY KEY,
        profile_id VARCHAR(50) NOT NULL,
        recommendation_data JSON NOT NULL,
        recommendation_type VARCHAR(20) NOT NULL,
        generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_paid BOOLEAN DEFAULT FALSE,
        paid_at TIMESTAMP NULL,
        FOREIGN KEY (profile_id) REFERENCES student_profiles(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 职业数据表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS careers (
        id VARCHAR(50) PRIMARY KEY,
        career_name VARCHAR(100) NOT NULL,
        career_category VARCHAR(50) NOT NULL,
        description TEXT,
        requirements JSON,
        salary_data JSON,
        employment_prospects JSON,
        related_majors JSON,
        development_path TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 模拟记录表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS simulations (
        id VARCHAR(50) PRIMARY KEY,
        profile_id VARCHAR(50) NOT NULL,
        selected_university VARCHAR(100) NOT NULL,
        selected_major VARCHAR(100) NOT NULL,
        target_career VARCHAR(100) NOT NULL,
        simulation_parameters JSON,
        simulation_result JSON NOT NULL,
        generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_paid BOOLEAN DEFAULT FALSE,
        paid_at TIMESTAMP NULL,
        FOREIGN KEY (profile_id) REFERENCES student_profiles(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 订单表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS orders (
        id VARCHAR(50) PRIMARY KEY,
        user_id VARCHAR(50) NOT NULL,
        product_type VARCHAR(50) NOT NULL,
        product_ids JSON,
        amount DECIMAL(10,2) NOT NULL,
        payment_method VARCHAR(20) NOT NULL,
        payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
        payment_time TIMESTAMP NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    logger.info('数据库表创建完成');
  } catch (error) {
    logger.error('创建数据库表失败:', error);
    throw error;
  }
}; 