<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>管理后台功能测试</h1>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>模态框测试</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testModal()">测试模态框</button>
                        <button class="btn btn-success" onclick="testAlert()">测试提示</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>API测试</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-info" onclick="testAPI()">测试API连接</button>
                        <div id="apiResult" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>导航测试</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline-primary me-2" onclick="testNavigation('users')">用户管理</button>
                        <button class="btn btn-outline-primary me-2" onclick="testNavigation('profiles')">考生档案</button>
                        <button class="btn btn-outline-primary me-2" onclick="testNavigation('universities')">院校专业</button>
                        <button class="btn btn-outline-primary me-2" onclick="testNavigation('careers')">职业数据</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 测试模态框 -->
    <div class="modal fade" id="testModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">测试模态框</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>这是一个测试模态框，用于验证Bootstrap功能是否正常。</p>
                    <form id="testForm">
                        <div class="mb-3">
                            <label for="testInput" class="form-label">测试输入</label>
                            <input type="text" class="form-control" id="testInput" placeholder="输入一些内容">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="saveTest()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const API_BASE = '/v1';
        let authToken = localStorage.getItem('admin_token');

        function testModal() {
            const modal = new bootstrap.Modal(document.getElementById('testModal'));
            modal.show();
        }

        function testAlert() {
            showAlert('这是一个测试提示', 'success');
        }

        function saveTest() {
            const input = document.getElementById('testInput').value;
            showAlert(`保存成功: ${input}`, 'success');
            bootstrap.Modal.getInstance(document.getElementById('testModal')).hide();
        }

        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        async function testAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> 测试中...';

            try {
                const response = await fetch(`${API_BASE}/admin/dashboard`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="text-success">✓ API连接成功</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="text-danger">✗ API错误: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="text-danger">✗ 网络错误: ${error.message}</div>`;
            }
        }

        function testNavigation(section) {
            showAlert(`导航到: ${section}`, 'info');
            // 这里可以添加实际的导航逻辑测试
        }

        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            if (!authToken) {
                showAlert('未登录，请先登录管理后台', 'warning');
            } else {
                showAlert('已登录，可以进行功能测试', 'success');
            }
        });
    </script>
</body>
</html>
