// 全局变量
let authToken = localStorage.getItem('admin_token');
let currentPage = 1;
let currentSection = 'dashboard';

// API基础URL
const API_BASE = '/v1';

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    if (authToken) {
        showMainPage();
        loadDashboard();
    } else {
        showLoginPage();
    }

    // 绑定登录表单事件
    document.getElementById('loginForm').addEventListener('submit', handleLogin);

    // 绑定导航事件
    document.querySelectorAll('[data-section]').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('data-section');
            showSection(section);
        });
    });

    // 绑定系统配置表单事件
    const systemConfigForm = document.getElementById('systemConfigForm');
    if (systemConfigForm) {
        systemConfigForm.addEventListener('submit', handleSystemConfigSave);
    }
});

// 显示登录页面
function showLoginPage() {
    document.getElementById('loginPage').classList.remove('d-none');
    document.getElementById('mainPage').classList.add('d-none');
}

// 显示主页面
function showMainPage() {
    document.getElementById('loginPage').classList.add('d-none');
    document.getElementById('mainPage').classList.remove('d-none');
}

// 处理登录
async function handleLogin(e) {
    e.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    try {
        const response = await fetch(`${API_BASE}/admin/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        const data = await response.json();

        if (data.code === 200) {
            authToken = data.data.access_token;
            localStorage.setItem('admin_token', authToken);
            showMainPage();
            loadDashboard();
            showAlert('登录成功', 'success');
        } else {
            showAlert(data.message || '登录失败', 'danger');
        }
    } catch (error) {
        console.error('登录错误:', error);
        showAlert('网络错误，请重试', 'danger');
    }
}

// 退出登录
function logout() {
    localStorage.removeItem('admin_token');
    authToken = null;
    showLoginPage();
    showAlert('已退出登录', 'info');
}

// 显示提示信息
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// API请求封装
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
        }
    };

    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };

    try {
        const response = await fetch(`${API_BASE}${url}`, finalOptions);
        const data = await response.json();

        if (response.status === 401) {
            logout();
            return null;
        }

        return data;
    } catch (error) {
        console.error('API请求错误:', error);
        showAlert('网络错误，请重试', 'danger');
        return null;
    }
}

// 显示指定区域
function showSection(section) {
    // 隐藏所有内容区域
    document.querySelectorAll('.content-section').forEach(el => {
        el.classList.add('d-none');
    });

    // 移除所有导航活动状态
    document.querySelectorAll('.nav-link').forEach(el => {
        el.classList.remove('active');
    });

    // 显示指定区域
    document.getElementById(section).classList.remove('d-none');

    // 设置导航活动状态
    document.querySelector(`[data-section="${section}"]`).classList.add('active');

    currentSection = section;

    // 加载对应数据
    switch(section) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'users':
            loadUsers();
            break;
        case 'profiles':
            loadProfiles();
            break;
        case 'universities':
            loadUniversities();
            break;
        case 'recommendations':
            loadRecommendations();
            break;
        case 'careers':
            loadCareers();
            break;
        case 'simulations':
            loadSimulations();
            break;
        case 'orders':
            loadOrders();
            break;
        case 'system':
            loadSystemConfig();
            break;
    }
}

// 加载控制台数据
async function loadDashboard() {
    try {
        // 显示加载状态
        document.getElementById('totalUsers').textContent = '...';
        document.getElementById('totalProfiles').textContent = '...';
        document.getElementById('totalRecommendations').textContent = '...';
        document.getElementById('totalOrders').textContent = '...';
        document.getElementById('recentActivities').innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';

        const data = await apiRequest('/admin/dashboard');
        if (data && data.code === 200) {
            const stats = data.data.stats;
            document.getElementById('totalUsers').textContent = stats.users || 0;
            document.getElementById('totalProfiles').textContent = stats.profiles || 0;
            document.getElementById('totalRecommendations').textContent = stats.recommendations || 0;
            document.getElementById('totalOrders').textContent = stats.orders || 0;

            // 显示最近活动
            const activities = data.data.activities || [];
            const activitiesHtml = activities.length > 0
                ? activities.map(activity => `
                    <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                        <div>
                            <strong>${activity.action}</strong><br>
                            <small class="text-muted">${activity.description}</small>
                        </div>
                        <small class="text-muted">${formatDate(activity.created_at)}</small>
                    </div>
                `).join('')
                : '<p class="text-muted">暂无活动记录</p>';

            document.getElementById('recentActivities').innerHTML = activitiesHtml;
        } else {
            // 错误处理
            document.getElementById('totalUsers').textContent = '-';
            document.getElementById('totalProfiles').textContent = '-';
            document.getElementById('totalRecommendations').textContent = '-';
            document.getElementById('totalOrders').textContent = '-';
            document.getElementById('recentActivities').innerHTML = '<p class="text-danger">加载失败</p>';
            showAlert('加载控制台数据失败', 'danger');
        }
    } catch (error) {
        console.error('加载控制台数据错误:', error);
        document.getElementById('recentActivities').innerHTML = '<p class="text-danger">加载失败</p>';
        showAlert('加载控制台数据失败', 'danger');
    }
}

// 加载用户列表
async function loadUsers(page = 1) {
    try {
        const tbody = document.querySelector('#usersTable tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="text-center"><div class="spinner-border" role="status"></div></td></tr>';

        const data = await apiRequest(`/admin/users?page=${page}&size=20`);
        if (data && data.code === 200) {
            const users = data.data.users || [];
            const pagination = data.data.pagination;

            if (users.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无用户数据</td></tr>';
            } else {
                tbody.innerHTML = users.map(user => `
                    <tr>
                        <td>${user.id}</td>
                        <td>${user.phone}</td>
                        <td>${user.nickname || '-'}</td>
                        <td>${formatDate(user.register_time)}</td>
                        <td>${user.last_login_time ? formatDate(user.last_login_time) : '-'}</td>
                        <td>
                            <span class="badge ${getStatusBadgeClass(user.status)}">${getStatusText(user.status)}</span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="viewUser('${user.id}')">查看</button>
                            <button class="btn btn-sm btn-outline-warning me-1" onclick="editUser('${user.id}')">编辑</button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteUser('${user.id}')">删除</button>
                        </td>
                    </tr>
                `).join('');
            }

            // 更新分页
            updatePagination('usersPagination', pagination, 'loadUsers');
        } else {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>';
            showAlert('加载用户列表失败', 'danger');
        }
    } catch (error) {
        console.error('加载用户列表错误:', error);
        const tbody = document.querySelector('#usersTable tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>';
        showAlert('加载用户列表失败', 'danger');
    }
}

// 加载考生档案
async function loadProfiles(page = 1) {
    try {
        const tbody = document.querySelector('#profilesTable tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="text-center"><div class="spinner-border" role="status"></div></td></tr>';

        const data = await apiRequest(`/admin/profiles?page=${page}&size=20`);
        if (data && data.code === 200) {
            const profiles = data.data.profiles || [];
            const pagination = data.data.pagination;

            if (profiles.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无档案数据</td></tr>';
            } else {
                tbody.innerHTML = profiles.map(profile => `
                    <tr>
                        <td>${profile.id}</td>
                        <td>${profile.name}</td>
                        <td>${profile.score}</td>
                        <td>${profile.province}</td>
                        <td>${profile.subject_combination}</td>
                        <td>${formatDate(profile.created_at)}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="viewProfile('${profile.id}')">查看</button>
                            <button class="btn btn-sm btn-outline-warning me-1" onclick="showEditProfileModal('${profile.id}')">编辑</button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteProfile('${profile.id}')">删除</button>
                        </td>
                    </tr>
                `).join('');
            }

            // 更新分页
            updatePagination('profilesPagination', pagination, 'loadProfiles');
        } else {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>';
            showAlert('加载档案列表失败', 'danger');
        }
    } catch (error) {
        console.error('加载档案列表错误:', error);
        const tbody = document.querySelector('#profilesTable tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>';
        showAlert('加载档案列表失败', 'danger');
    }
}

// 加载院校专业
async function loadUniversities(page = 1) {
    try {
        const tbody = document.querySelector('#universitiesTable tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="text-center"><div class="spinner-border" role="status"></div></td></tr>';

        const data = await apiRequest(`/admin/universities?page=${page}&size=20`);
        if (data && data.code === 200) {
            const universities = data.data.universities || [];
            const pagination = data.data.pagination;

            if (universities.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无院校专业数据</td></tr>';
            } else {
                tbody.innerHTML = universities.map(uni => `
                    <tr>
                        <td>${uni.id}</td>
                        <td>${uni.university_name}</td>
                        <td>${uni.major_name}</td>
                        <td>
                            <span class="badge ${getTierBadgeClass(uni.university_tier)}">${uni.university_tier}</span>
                        </td>
                        <td>${uni.province}</td>
                        <td>${uni.city}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="viewUniversity('${uni.id}')">查看</button>
                            <button class="btn btn-sm btn-outline-warning me-1" onclick="showEditUniversityModal('${uni.id}')">编辑</button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteUniversity('${uni.id}')">删除</button>
                        </td>
                    </tr>
                `).join('');
            }

            // 更新分页
            updatePagination('universitiesPagination', pagination, 'loadUniversities');
        } else {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>';
            showAlert('加载院校专业列表失败', 'danger');
        }
    } catch (error) {
        console.error('加载院校专业列表错误:', error);
        const tbody = document.querySelector('#universitiesTable tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>';
        showAlert('加载院校专业列表失败', 'danger');
    }
}

// 加载推荐记录
async function loadRecommendations(page = 1) {
    try {
        const tbody = document.querySelector('#recommendationsTable tbody');
        tbody.innerHTML = '<tr><td colspan="6" class="text-center"><div class="spinner-border" role="status"></div></td></tr>';

        const data = await apiRequest(`/admin/recommendations?page=${page}&size=20`);
        if (data && data.code === 200) {
            const recommendations = data.data.recommendations || [];
            const pagination = data.data.pagination;

            if (recommendations.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">暂无推荐记录</td></tr>';
            } else {
                tbody.innerHTML = recommendations.map(rec => `
                    <tr>
                        <td>${rec.id}</td>
                        <td>${rec.profile_id}</td>
                        <td>${rec.recommendation_type}</td>
                        <td>${formatDate(rec.generated_at)}</td>
                        <td>
                            <span class="badge ${rec.is_paid ? 'bg-success' : 'bg-secondary'}">${rec.is_paid ? '已付费' : '未付费'}</span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewRecommendation('${rec.id}')">查看</button>
                        </td>
                    </tr>
                `).join('');
            }

            // 更新分页
            updatePagination('recommendationsPagination', pagination, 'loadRecommendations');
        } else {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">加载失败</td></tr>';
            showAlert('加载推荐记录失败', 'danger');
        }
    } catch (error) {
        console.error('加载推荐记录错误:', error);
        const tbody = document.querySelector('#recommendationsTable tbody');
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">加载失败</td></tr>';
        showAlert('加载推荐记录失败', 'danger');
    }
}

// 加载职业数据
async function loadCareers(page = 1) {
    try {
        const tbody = document.querySelector('#careersTable tbody');
        tbody.innerHTML = '<tr><td colspan="5" class="text-center"><div class="spinner-border" role="status"></div></td></tr>';

        const data = await apiRequest(`/admin/careers?page=${page}&size=20`);
        if (data && data.code === 200) {
            const careers = data.data.careers || [];
            const pagination = data.data.pagination;

            if (careers.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">暂无职业数据</td></tr>';
            } else {
                tbody.innerHTML = careers.map(career => `
                    <tr>
                        <td>${career.id}</td>
                        <td>${career.career_name}</td>
                        <td>${career.career_category}</td>
                        <td>${formatDate(career.created_at)}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="viewCareer('${career.id}')">查看</button>
                            <button class="btn btn-sm btn-outline-warning me-1" onclick="showEditCareerModal('${career.id}')">编辑</button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCareer('${career.id}')">删除</button>
                        </td>
                    </tr>
                `).join('');
            }

            // 更新分页
            updatePagination('careersPagination', pagination, 'loadCareers');
        } else {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center text-danger">加载失败</td></tr>';
            showAlert('加载职业数据失败', 'danger');
        }
    } catch (error) {
        console.error('加载职业数据错误:', error);
        const tbody = document.querySelector('#careersTable tbody');
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-danger">加载失败</td></tr>';
        showAlert('加载职业数据失败', 'danger');
    }
}

// 加载模拟记录
async function loadSimulations(page = 1) {
    try {
        const tbody = document.querySelector('#simulationsTable tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="text-center"><div class="spinner-border" role="status"></div></td></tr>';

        const data = await apiRequest(`/admin/simulations?page=${page}&size=20`);
        if (data && data.code === 200) {
            const simulations = data.data.simulations || [];
            const pagination = data.data.pagination;

            if (simulations.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无模拟记录</td></tr>';
            } else {
                tbody.innerHTML = simulations.map(sim => `
                    <tr>
                        <td>${sim.id}</td>
                        <td>${sim.profile_id}</td>
                        <td>${sim.selected_university}</td>
                        <td>${sim.selected_major}</td>
                        <td>${sim.target_career}</td>
                        <td>${formatDate(sim.generated_at)}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewSimulation('${sim.id}')">查看</button>
                        </td>
                    </tr>
                `).join('');
            }

            // 更新分页
            updatePagination('simulationsPagination', pagination, 'loadSimulations');
        } else {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>';
            showAlert('加载模拟记录失败', 'danger');
        }
    } catch (error) {
        console.error('加载模拟记录错误:', error);
        const tbody = document.querySelector('#simulationsTable tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>';
        showAlert('加载模拟记录失败', 'danger');
    }
}

// 加载订单列表
async function loadOrders(page = 1) {
    try {
        const tbody = document.querySelector('#ordersTable tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="text-center"><div class="spinner-border" role="status"></div></td></tr>';

        const data = await apiRequest(`/admin/orders?page=${page}&size=20`);
        if (data && data.code === 200) {
            const orders = data.data.orders || [];
            const pagination = data.data.pagination;

            if (orders.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无订单数据</td></tr>';
            } else {
                tbody.innerHTML = orders.map(order => `
                    <tr>
                        <td>${order.id}</td>
                        <td>${order.user_id}</td>
                        <td>${order.product_type}</td>
                        <td>¥${order.amount}</td>
                        <td>
                            <span class="badge ${getPaymentStatusBadgeClass(order.payment_status)}">${getPaymentStatusText(order.payment_status)}</span>
                        </td>
                        <td>${formatDate(order.created_at)}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewOrder('${order.id}')">查看</button>
                        </td>
                    </tr>
                `).join('');
            }

            // 更新分页
            updatePagination('ordersPagination', pagination, 'loadOrders');
        } else {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>';
            showAlert('加载订单列表失败', 'danger');
        }
    } catch (error) {
        console.error('加载订单列表错误:', error);
        const tbody = document.querySelector('#ordersTable tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>';
        showAlert('加载订单列表失败', 'danger');
    }
}

// 加载系统配置
async function loadSystemConfig() {
    const data = await apiRequest('/admin/config');
    if (data && data.code === 200) {
        const config = data.data;
        document.getElementById('currentYear').value = config.current_year;
        document.getElementById('detailedAnalysisPrice').value = config.pricing.detailed_analysis;
        document.getElementById('simulationReportPrice').value = config.pricing.simulation_report;
    }

    // 检查系统状态
    checkSystemStatus();
}

// 检查系统状态
async function checkSystemStatus() {
    const data = await apiRequest('/admin/status');
    if (data && data.code === 200) {
        const status = data.data;

        // 更新状态显示
        const statusElements = {
            database: document.querySelector('.card-body .mb-2:nth-child(1) .badge'),
            redis: document.querySelector('.card-body .mb-2:nth-child(2) .badge'),
            api: document.querySelector('.card-body .mb-2:nth-child(3) .badge')
        };

        Object.keys(statusElements).forEach(key => {
            const element = statusElements[key];
            if (element) {
                element.className = `badge ${status[key] ? 'bg-success' : 'bg-danger'}`;
                element.textContent = status[key] ? '正常' : '异常';
            }
        });
    }
}

// 工具函数
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

function getStatusBadgeClass(status) {
    const classes = {
        'active': 'bg-success',
        'inactive': 'bg-warning',
        'banned': 'bg-danger'
    };
    return classes[status] || 'bg-secondary';
}

function getStatusText(status) {
    const texts = {
        'active': '正常',
        'inactive': '未激活',
        'banned': '已封禁'
    };
    return texts[status] || status;
}

function getTierBadgeClass(tier) {
    const classes = {
        '985': 'bg-danger',
        '211': 'bg-warning',
        'tier1': 'bg-primary',
        'tier2': 'bg-info',
        'tier3': 'bg-secondary',
        'specialist': 'bg-dark'
    };
    return classes[tier] || 'bg-secondary';
}

function getPaymentStatusBadgeClass(status) {
    const classes = {
        'pending': 'bg-warning',
        'paid': 'bg-success',
        'failed': 'bg-danger',
        'refunded': 'bg-secondary'
    };
    return classes[status] || 'bg-secondary';
}

function getPaymentStatusText(status) {
    const texts = {
        'pending': '待支付',
        'paid': '已支付',
        'failed': '支付失败',
        'refunded': '已退款'
    };
    return texts[status] || status;
}

// 更新分页组件
function updatePagination(containerId, pagination, loadFunctionName) {
    const container = document.getElementById(containerId);
    if (!container || !pagination) return;

    const { current_page, total_pages, total_count } = pagination;

    if (total_pages <= 1) {
        container.innerHTML = '';
        return;
    }

    let html = '';

    // 上一页
    html += `<li class="page-item ${current_page <= 1 ? 'disabled' : ''}">
        <a class="page-link" href="#" onclick="${loadFunctionName}(${current_page - 1}); return false;">上一页</a>
    </li>`;

    // 页码
    const startPage = Math.max(1, current_page - 2);
    const endPage = Math.min(total_pages, current_page + 2);

    if (startPage > 1) {
        html += `<li class="page-item">
            <a class="page-link" href="#" onclick="${loadFunctionName}(1); return false;">1</a>
        </li>`;
        if (startPage > 2) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        html += `<li class="page-item ${i === current_page ? 'active' : ''}">
            <a class="page-link" href="#" onclick="${loadFunctionName}(${i}); return false;">${i}</a>
        </li>`;
    }

    if (endPage < total_pages) {
        if (endPage < total_pages - 1) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        html += `<li class="page-item">
            <a class="page-link" href="#" onclick="${loadFunctionName}(${total_pages}); return false;">${total_pages}</a>
        </li>`;
    }

    // 下一页
    html += `<li class="page-item ${current_page >= total_pages ? 'disabled' : ''}">
        <a class="page-link" href="#" onclick="${loadFunctionName}(${current_page + 1}); return false;">下一页</a>
    </li>`;

    container.innerHTML = html;
}

function getTierBadgeClass(tier) {
    const classes = {
        '985': 'bg-danger',
        '211': 'bg-warning',
        'tier1': 'bg-info',
        'tier2': 'bg-secondary'
    };
    return classes[tier] || 'bg-secondary';
}

function getPaymentStatusBadgeClass(status) {
    const classes = {
        'pending': 'bg-warning',
        'paid': 'bg-success',
        'failed': 'bg-danger',
        'refunded': 'bg-info'
    };
    return classes[status] || 'bg-secondary';
}

function getPaymentStatusText(status) {
    const texts = {
        'pending': '待支付',
        'paid': '已支付',
        'failed': '支付失败',
        'refunded': '已退款'
    };
    return texts[status] || status;
}

// 分页更新
function updatePagination(containerId, pagination, loadFunctionName) {
    const container = document.getElementById(containerId);
    if (!container || !pagination) return;

    const { current_page, total_pages } = pagination;
    let html = '';

    // 上一页
    html += `<li class="page-item ${current_page <= 1 ? 'disabled' : ''}">
        <a class="page-link" href="#" onclick="${loadFunctionName}(${current_page - 1}); return false;">上一页</a>
    </li>`;

    // 页码
    for (let i = Math.max(1, current_page - 2); i <= Math.min(total_pages, current_page + 2); i++) {
        html += `<li class="page-item ${i === current_page ? 'active' : ''}">
            <a class="page-link" href="#" onclick="${loadFunctionName}(${i}); return false;">${i}</a>
        </li>`;
    }

    // 下一页
    html += `<li class="page-item ${current_page >= total_pages ? 'disabled' : ''}">
        <a class="page-link" href="#" onclick="${loadFunctionName}(${current_page + 1}); return false;">下一页</a>
    </li>`;

    container.innerHTML = html;
}

// 用户管理功能
function showAddUserModal() {
    document.getElementById('userModalTitle').textContent = '添加用户';
    document.getElementById('userForm').reset();
    document.getElementById('userId').value = '';
    const modal = new bootstrap.Modal(document.getElementById('userModal'));
    modal.show();
}

function showEditUserModal(id) {
    document.getElementById('userModalTitle').textContent = '编辑用户';
    loadUserDetails(id);
    const modal = new bootstrap.Modal(document.getElementById('userModal'));
    modal.show();
}

async function loadUserDetails(id) {
    try {
        const data = await apiRequest(`/admin/users/${id}`);
        if (data && data.code === 200) {
            const user = data.data;
            document.getElementById('userId').value = user.id;
            document.getElementById('userPhone').value = user.phone;
            document.getElementById('userNickname').value = user.nickname || '';
            document.getElementById('userStatus').value = user.status;
        } else {
            showAlert('加载用户详情失败', 'danger');
        }
    } catch (error) {
        console.error('加载用户详情错误:', error);
        showAlert('加载用户详情失败', 'danger');
    }
}

async function saveUser() {
    const form = document.getElementById('userForm');
    const formData = new FormData(form);
    const userId = formData.get('userId');

    const userData = {
        phone: formData.get('phone'),
        nickname: formData.get('nickname'),
        status: formData.get('status')
    };

    try {
        let data;
        if (userId) {
            // 更新用户
            data = await apiRequest(`/admin/users/${userId}`, {
                method: 'PUT',
                body: JSON.stringify(userData)
            });
        } else {
            // 添加用户
            data = await apiRequest('/admin/users', {
                method: 'POST',
                body: JSON.stringify(userData)
            });
        }

        if (data && data.code === 200) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('userModal')).hide();
            loadUsers(); // 刷新用户列表
        } else {
            showAlert(data?.message || '操作失败', 'danger');
        }
    } catch (error) {
        console.error('保存用户错误:', error);
        showAlert('保存用户失败', 'danger');
    }
}

function deleteUser(id) {
    document.getElementById('deleteConfirmText').textContent = '确定要删除这个用户吗？此操作将同时删除用户的所有相关数据，且不可恢复。';
    document.getElementById('confirmDeleteBtn').onclick = () => confirmDeleteUser(id);
    const modal = new bootstrap.Modal(document.getElementById('confirmDeleteModal'));
    modal.show();
}

async function confirmDeleteUser(id) {
    try {
        const data = await apiRequest(`/admin/users/${id}`, {
            method: 'DELETE'
        });

        if (data && data.code === 200) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('confirmDeleteModal')).hide();
            loadUsers(); // 刷新用户列表
        } else {
            showAlert(data?.message || '删除失败', 'danger');
        }
    } catch (error) {
        console.error('删除用户错误:', error);
        showAlert('删除用户失败', 'danger');
    }
}

function viewUser(id) {
    showEditUserModal(id);
}

function editUser(id) {
    showEditUserModal(id);
}

// 考生档案管理功能
function showAddProfileModal() {
    document.getElementById('profileModalTitle').textContent = '添加考生档案';
    document.getElementById('profileForm').reset();
    document.getElementById('profileId').value = '';
    const modal = new bootstrap.Modal(document.getElementById('profileModal'));
    modal.show();
}

function showEditProfileModal(id) {
    document.getElementById('profileModalTitle').textContent = '编辑考生档案';
    loadProfileDetails(id);
    const modal = new bootstrap.Modal(document.getElementById('profileModal'));
    modal.show();
}

async function loadProfileDetails(id) {
    try {
        const data = await apiRequest(`/admin/profiles/${id}`);
        if (data && data.code === 200) {
            const profile = data.data;
            document.getElementById('profileId').value = profile.id;
            document.getElementById('profileUserId').value = profile.user_id;
            document.getElementById('profileName').value = profile.name;
            document.getElementById('profileScore').value = profile.score;
            document.getElementById('profileProvince').value = profile.province;
            document.getElementById('profileSubjectCombination').value = profile.subject_combination;
            document.getElementById('profileExamType').value = profile.exam_type;
            document.getElementById('profileFamilyEconomicStatus').value = profile.family_economic_status || '';
        } else {
            showAlert('加载档案详情失败', 'danger');
        }
    } catch (error) {
        console.error('加载档案详情错误:', error);
        showAlert('加载档案详情失败', 'danger');
    }
}

async function saveProfile() {
    const form = document.getElementById('profileForm');
    const formData = new FormData(form);
    const profileId = formData.get('profileId');

    const profileData = {
        user_id: formData.get('user_id'),
        name: formData.get('name'),
        score: parseInt(formData.get('score')),
        province: formData.get('province'),
        subject_combination: formData.get('subject_combination'),
        exam_type: formData.get('exam_type'),
        family_economic_status: formData.get('family_economic_status')
    };

    try {
        let data;
        if (profileId) {
            // 更新档案
            data = await apiRequest(`/admin/profiles/${profileId}`, {
                method: 'PUT',
                body: JSON.stringify(profileData)
            });
        } else {
            // 添加档案
            data = await apiRequest('/admin/profiles', {
                method: 'POST',
                body: JSON.stringify(profileData)
            });
        }

        if (data && data.code === 200) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('profileModal')).hide();
            loadProfiles(); // 刷新档案列表
        } else {
            showAlert(data?.message || '操作失败', 'danger');
        }
    } catch (error) {
        console.error('保存档案错误:', error);
        showAlert('保存档案失败', 'danger');
    }
}

function deleteProfile(id) {
    document.getElementById('deleteConfirmText').textContent = '确定要删除这个考生档案吗？此操作将同时删除档案的所有相关数据，且不可恢复。';
    document.getElementById('confirmDeleteBtn').onclick = () => confirmDeleteProfile(id);
    const modal = new bootstrap.Modal(document.getElementById('confirmDeleteModal'));
    modal.show();
}

async function confirmDeleteProfile(id) {
    try {
        const data = await apiRequest(`/admin/profiles/${id}`, {
            method: 'DELETE'
        });

        if (data && data.code === 200) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('confirmDeleteModal')).hide();
            loadProfiles(); // 刷新档案列表
        } else {
            showAlert(data?.message || '删除失败', 'danger');
        }
    } catch (error) {
        console.error('删除档案错误:', error);
        showAlert('删除档案失败', 'danger');
    }
}

function viewProfile(id) {
    showEditProfileModal(id);
}

// 院校专业管理功能
function showAddUniversityModal() {
    document.getElementById('universityModalTitle').textContent = '添加院校专业';
    document.getElementById('universityForm').reset();
    document.getElementById('universityId').value = '';
    const modal = new bootstrap.Modal(document.getElementById('universityModal'));
    modal.show();
}

function showEditUniversityModal(id) {
    document.getElementById('universityModalTitle').textContent = '编辑院校专业';
    loadUniversityDetails(id);
    const modal = new bootstrap.Modal(document.getElementById('universityModal'));
    modal.show();
}

async function loadUniversityDetails(id) {
    try {
        const data = await apiRequest(`/admin/universities/${id}`);
        if (data && data.code === 200) {
            const university = data.data;
            document.getElementById('universityId').value = university.id;
            document.getElementById('universityName').value = university.university_name;
            document.getElementById('majorName').value = university.major_name;
            document.getElementById('universityTier').value = university.university_tier;
            document.getElementById('universityProvince').value = university.province;
            document.getElementById('universityCity').value = university.city;
            document.getElementById('majorCategory').value = university.major_category || '';
            document.getElementById('admissionScore').value = university.admission_score || '';
        } else {
            showAlert('加载院校专业详情失败', 'danger');
        }
    } catch (error) {
        console.error('加载院校专业详情错误:', error);
        showAlert('加载院校专业详情失败', 'danger');
    }
}

async function saveUniversity() {
    const form = document.getElementById('universityForm');
    const formData = new FormData(form);
    const universityId = formData.get('universityId');

    const universityData = {
        university_name: formData.get('university_name'),
        major_name: formData.get('major_name'),
        university_tier: formData.get('university_tier'),
        province: formData.get('province'),
        city: formData.get('city'),
        major_category: formData.get('major_category'),
        admission_score: formData.get('admission_score') ? parseInt(formData.get('admission_score')) : null
    };

    try {
        let data;
        if (universityId) {
            // 更新院校专业
            data = await apiRequest(`/admin/universities/${universityId}`, {
                method: 'PUT',
                body: JSON.stringify(universityData)
            });
        } else {
            // 添加院校专业
            data = await apiRequest('/admin/universities', {
                method: 'POST',
                body: JSON.stringify(universityData)
            });
        }

        if (data && data.code === 200) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('universityModal')).hide();
            loadUniversities(); // 刷新院校专业列表
        } else {
            showAlert(data?.message || '操作失败', 'danger');
        }
    } catch (error) {
        console.error('保存院校专业错误:', error);
        showAlert('保存院校专业失败', 'danger');
    }
}

function deleteUniversity(id) {
    document.getElementById('deleteConfirmText').textContent = '确定要删除这个院校专业吗？此操作不可恢复。';
    document.getElementById('confirmDeleteBtn').onclick = () => confirmDeleteUniversity(id);
    const modal = new bootstrap.Modal(document.getElementById('confirmDeleteModal'));
    modal.show();
}

async function confirmDeleteUniversity(id) {
    try {
        const data = await apiRequest(`/admin/universities/${id}`, {
            method: 'DELETE'
        });

        if (data && data.code === 200) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('confirmDeleteModal')).hide();
            loadUniversities(); // 刷新院校专业列表
        } else {
            showAlert(data?.message || '删除失败', 'danger');
        }
    } catch (error) {
        console.error('删除院校专业错误:', error);
        showAlert('删除院校专业失败', 'danger');
    }
}

function viewUniversity(id) {
    showEditUniversityModal(id);
}

// 职业管理功能
function showAddCareerModal() {
    document.getElementById('careerModalTitle').textContent = '添加职业';
    document.getElementById('careerForm').reset();
    document.getElementById('careerId').value = '';
    const modal = new bootstrap.Modal(document.getElementById('careerModal'));
    modal.show();
}

function showEditCareerModal(id) {
    document.getElementById('careerModalTitle').textContent = '编辑职业';
    loadCareerDetails(id);
    const modal = new bootstrap.Modal(document.getElementById('careerModal'));
    modal.show();
}

async function loadCareerDetails(id) {
    try {
        const data = await apiRequest(`/admin/careers/${id}`);
        if (data && data.code === 200) {
            const career = data.data;
            document.getElementById('careerId').value = career.id;
            document.getElementById('careerName').value = career.career_name;
            document.getElementById('careerCategory').value = career.career_category;
            document.getElementById('careerDescription').value = career.description || '';
            document.getElementById('averageSalary').value = career.average_salary || '';
            document.getElementById('growthProspect').value = career.growth_prospect || '';
        } else {
            showAlert('加载职业详情失败', 'danger');
        }
    } catch (error) {
        console.error('加载职业详情错误:', error);
        showAlert('加载职业详情失败', 'danger');
    }
}

async function saveCareer() {
    const form = document.getElementById('careerForm');
    const formData = new FormData(form);
    const careerId = formData.get('careerId');

    const careerData = {
        career_name: formData.get('career_name'),
        career_category: formData.get('career_category'),
        description: formData.get('description'),
        average_salary: formData.get('average_salary') ? parseInt(formData.get('average_salary')) : null,
        growth_prospect: formData.get('growth_prospect')
    };

    try {
        let data;
        if (careerId) {
            // 更新职业
            data = await apiRequest(`/admin/careers/${careerId}`, {
                method: 'PUT',
                body: JSON.stringify(careerData)
            });
        } else {
            // 添加职业
            data = await apiRequest('/admin/careers', {
                method: 'POST',
                body: JSON.stringify(careerData)
            });
        }

        if (data && data.code === 200) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('careerModal')).hide();
            loadCareers(); // 刷新职业列表
        } else {
            showAlert(data?.message || '操作失败', 'danger');
        }
    } catch (error) {
        console.error('保存职业错误:', error);
        showAlert('保存职业失败', 'danger');
    }
}

function deleteCareer(id) {
    document.getElementById('deleteConfirmText').textContent = '确定要删除这个职业吗？此操作不可恢复。';
    document.getElementById('confirmDeleteBtn').onclick = () => confirmDeleteCareer(id);
    const modal = new bootstrap.Modal(document.getElementById('confirmDeleteModal'));
    modal.show();
}

async function confirmDeleteCareer(id) {
    try {
        const data = await apiRequest(`/admin/careers/${id}`, {
            method: 'DELETE'
        });

        if (data && data.code === 200) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('confirmDeleteModal')).hide();
            loadCareers(); // 刷新职业列表
        } else {
            showAlert(data?.message || '删除失败', 'danger');
        }
    } catch (error) {
        console.error('删除职业错误:', error);
        showAlert('删除职业失败', 'danger');
    }
}

function viewCareer(id) {
    showEditCareerModal(id);
}

// 其他查看功能（占位符）
function viewRecommendation(id) {
    showAlert(`查看推荐 ${id}`, 'info');
}

function viewSimulation(id) {
    showAlert(`查看模拟 ${id}`, 'info');
}

function viewOrder(id) {
    showAlert(`查看订单 ${id}`, 'info');
}

function saveData() {
    showAlert('保存功能开发中', 'info');
}

// 处理系统配置保存
async function handleSystemConfigSave(e) {
    e.preventDefault();

    const currentYear = document.getElementById('currentYear').value;
    const detailedAnalysisPrice = document.getElementById('detailedAnalysisPrice').value;
    const simulationReportPrice = document.getElementById('simulationReportPrice').value;

    const configData = {
        current_year: parseInt(currentYear),
        pricing: {
            detailed_analysis: parseFloat(detailedAnalysisPrice),
            simulation_report: parseFloat(simulationReportPrice),
            premium_package: 499
        }
    };

    try {
        const data = await apiRequest('/admin/config', {
            method: 'PUT',
            body: JSON.stringify(configData)
        });

        if (data && data.code === 200) {
            showAlert('系统配置保存成功', 'success');
        } else {
            showAlert(data?.message || '保存失败', 'danger');
        }
    } catch (error) {
        console.error('保存配置错误:', error);
        showAlert('保存失败，请重试', 'danger');
    }
}

// 添加一些测试数据的功能
async function addTestData() {
    try {
        // 这里可以添加一些测试数据
        showAlert('测试数据添加功能开发中', 'info');
    } catch (error) {
        console.error('添加测试数据错误:', error);
        showAlert('添加测试数据失败', 'danger');
    }
}

// 导出数据功能
function exportData(type) {
    showAlert(`导出${type}数据功能开发中`, 'info');
}

// 刷新当前页面数据
function refreshCurrentData() {
    switch(currentSection) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'users':
            loadUsers();
            break;
        case 'profiles':
            loadProfiles();
            break;
        case 'universities':
            loadUniversities();
            break;
        case 'recommendations':
            loadRecommendations();
            break;
        case 'careers':
            loadCareers();
            break;
        case 'simulations':
            loadSimulations();
            break;
        case 'orders':
            loadOrders();
            break;
        case 'system':
            loadSystemConfig();
            break;
    }
    showAlert('数据已刷新', 'success');
}

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('页面错误:', e.error);
    showAlert('页面发生错误，请刷新重试', 'danger');
});

// 添加键盘快捷键支持
document.addEventListener('keydown', function(e) {
    // Ctrl+R 刷新数据
    if (e.ctrlKey && e.key === 'r') {
        e.preventDefault();
        refreshCurrentData();
    }

    // ESC 关闭模态框
    if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        });
    }
});